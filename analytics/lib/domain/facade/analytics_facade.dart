import 'package:analytics/domain/models/therapy_session_event_model.dart';
import 'package:fpdart/fpdart.dart';
import 'package:remote/domain/model/device_info_model.dart';
import '../models/device_settings_model.dart';

import '../models/therapy_session_model.dart';

/// Unified facade for all analytics operations
abstract class IAnalyticsFacade {
  Future<Either<String, Unit>> saveDeviceSnapshot({
    required String sessionId,
    required Map<String, dynamic> snapshot,
    DateTime? timestamp,
  });

  // Enhanced therapy session management
  Future<Either<String, String>> startTherapySession();

  Future<Either<String, Unit>> endTherapySession();

  Future<Either<String, Unit>> logSettingChange({
    required TherapySessionEventModel event,
  });

  // Session data retrieval
  Future<Either<String, List<TherapySessionModel>>> getStoredSessions();
  Future<Either<String, List<TherapySessionModel>>> getUnsyncedSessions();
  Future<Either<String, TherapySessionModel?>> getCurrentSession();

  // Sync operations
  Future<Either<String, Unit>> syncSessionsToCloud();
  Future<Either<String, Unit>>
      forceResyncAllSessions(); // For debugging sync issues
  Future<Either<String, Unit>> saveDeviceLog({
    required String sessionId,
    required Map<String, dynamic> log,
    DateTime? timestamp,
  });
  Future<Either<String, List<TherapySessionModel>>> getTherapySessionsLocal();
  Future<Either<String, Unit>> syncTherapySessionsToCloud();

  // Session management operations

  Future<Either<String, Map<String, dynamic>>> getMostUsedSettings();
  //get current session id
  Future<String?> getCurrentSessionId();

  // === 🎯 Feedback operations ===
  Future<Either<String, Unit>> submitSessionFeedback({
    required String sessionId,
    String? feedbackText,
    int? painLevelBefore,
    int? painLevelAfter,
  });

  Future<Either<String, Unit>> scheduleSessionFeedbackNotification({
    required String sessionId,
    required Duration delay,
  });

  Future<Either<String, Unit>> markFeedbackRequested(String sessionId);

  // === 🎯 Feedback trigger operations ===
  /// Stream that emits session IDs when feedback should be shown
  Stream<String> get feedbackTriggerStream;

  /// Check if notification payload contains a session that needs feedback
  Future<void> checkNotificationPayload(String? payload);

  /// Check if most recent session needs feedback (app opened without notification)
  Future<void> checkMostRecentSession();
}
