import 'package:authentication/domain/facade/i_auth_facade.dart';
import 'package:authentication/repository/auth_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:injectable/injectable.dart';

import '../../domain/models/therapy_session_model.dart';

abstract class ICloudTelemetryDataSource {
  Future<bool> saveTherapySession(TherapySessionModel session);
}

@LazySingleton(as: ICloudTelemetryDataSource)
class FirebaseTelemetryDataSource implements ICloudTelemetryDataSource {
  final FirebaseFirestore _firestore;
  final IAuthFacade _authFacade;

  FirebaseTelemetryDataSource(this._firestore, this._authFacade);

  @override
  Future<bool> saveTherapySession(TherapySessionModel session) async {
    try {
      // ignore: avoid_print
      print(
          '🔄 Attempting to save therapy session to Firestore: ${session.sessionInfo.sessionId}');

      final userOption = await _authFacade.getSignedInUser();
      final user = userOption.getOrNull();
      if (user == null) {
        return false;
      }
      final updatedSession = session.copyWith(
        sessionInfo: session.sessionInfo.copyWith(userId: user.uid),
      );
      final firestoreData = updatedSession.toJson();
      firestoreData.addAll({'syncedAt': FieldValue.serverTimestamp()});
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('therapy_sessions')
          .doc(updatedSession.sessionInfo.sessionId)
          .set(firestoreData);
      return true;
    } catch (e) {
      // ignore: avoid_print
      print('❌ Failed to save therapy session: $e');
      return false;
    }
  }
}
